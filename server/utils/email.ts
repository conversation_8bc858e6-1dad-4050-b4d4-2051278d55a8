import nodemailer from 'nodemailer'

interface ContactFormData {
  name: string
  email: string
  message: string
}

function escapeHtml(text: string): string {
  return text.replace(/[&<>"']/g, m => ({
    '&': '&amp;',
    '<': '&lt;',
    '>': '&gt;',
    '"': '&quot;',
    '\'': '&#039;'
  })[m]!)
}

function sanitizeHeaderField(str: string): string {
  return str.replace(/[\r\n]/g, '').trim()
}

function isValidEmail(email: string): boolean {
  return /^[^\s@]+@[^\s@]+\.[^\s@]+$/.test(email)
}

export async function sendContactEmailNew(data: ContactFormData) {
  const config = useRuntimeConfig()

  // Sanitize and validate inputs
  const name = sanitizeHeaderField(data.name)
  const email = sanitizeHeaderField(data.email)
  const message = data.message?.toString() || ''

  if (!isValidEmail(email)) {
    throw new Error('Invalid email address')
  }

  if (message.length > 5000) {
    throw new Error('Message too long')
  }

  if (!config.emailUser || !config.emailPassword) {
    console.warn('Email credentials not configured. Skipping email notification.')
    console.log('Mock Email: Would send <NAME_EMAIL>')
    console.log('Mock Email Data:', { from: email, name, message })
    return { success: true, messageId: 'mock-email-id-' + Date.now() }
  }

  const transporter = nodemailer.createTransport({
    host: 'smtp.zoho.com.au',
    port: 465,
    secure: true,
    auth: {
      user: config.emailUser,
      pass: config.emailPassword
    }
  })

  const escapedMessage = escapeHtml(message).replace(/\n/g, '<br>')

  const mailOptions = {
    from: config.emailUser,
    to: '<EMAIL>',
    bcc: [
      '<EMAIL>',
      '<EMAIL>',
      '<EMAIL>'
    ],
    subject: `New Contact Message from ${name}`,
    html: `
      <div style="font-family: Arial, sans-serif; max-width: 600px; margin: 0 auto;">
        <h2 style="color: #333; border-bottom: 2px solid #f97316; padding-bottom: 10px;">
          New Contact Form Submission
        </h2>

        <div style="background-color: #f9f9f9; padding: 20px; border-radius: 8px; margin: 20px 0;">
          <h3 style="color: #333; margin-top: 0;">Contact Details:</h3>
          <p><strong>Name:</strong> ${escapeHtml(name)}</p>
          <p><strong>Email:</strong> ${escapeHtml(email)}</p>
        </div>

        <div style="background-color: #fff; padding: 20px; border: 1px solid #ddd; border-radius: 8px;">
          <h3 style="color: #333; margin-top: 0;">Message:</h3>
          <p style="line-height: 1.6; color: #555;">${escapedMessage}</p>
        </div>

        <div style="margin-top: 20px; padding: 15px; background-color: #fef3cd; border-radius: 8px; border-left: 4px solid #f97316;">
          <p style="margin: 0; color: #856404;">
            <strong>Action Required:</strong> Please respond to this inquiry within 24 hours.
          </p>
        </div>

        <hr style="margin: 30px 0; border: none; border-top: 1px solid #ddd;">

        <p style="color: #666; font-size: 12px; text-align: center;">
          This email was sent from the Daily Bread Media contact form.<br>
          Reply directly to this email to respond to ${escapeHtml(name)}.
        </p>
      </div>
    `,
    replyTo: email
  }

  try {
    const info = await transporter.sendMail(mailOptions)
    console.log('Email sent successfully:', info.messageId)
    return { success: true, messageId: info.messageId }
  } catch (error) {
    console.error('Error sending email:', error)
    if (error instanceof Error) {
      const smtpError = error as Error & {
        code?: string
        command?: string
        response?: string
      }
      console.error('SMTP error details:', {
        message: smtpError.message,
        code: smtpError.code,
        command: smtpError.command,
        response: smtpError.response
      })
    }
    throw new Error('Failed to send email notification')
  }
}
