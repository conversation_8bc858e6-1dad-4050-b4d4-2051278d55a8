import { initializeApp, getApps, cert, type App } from 'firebase-admin/app'
import { getFirestore, type Firestore } from 'firebase-admin/firestore'

// Define types for mock database
interface MockDocumentReference {
  id: string
}

interface MockCollection {
  add: (data: Record<string, unknown>) => Promise<MockDocumentReference>
}

interface MockDatabase {
  collection: (name: string) => MockCollection
}

// Create mock database for development
function createMockDatabase(): MockDatabase {
  return {
    collection: () => ({
      add: async (data: Record<string, unknown>) => {
        console.log('Mock Firebase: Would save data:', data)
        return { id: 'mock-doc-id-' + Date.now() }
      }
    })
  }
}

// Initialize Firebase Admin SDK
function initializeFirebase(): { db: Firestore | MockDatabase, app?: App } {
  try {
    if (getApps().length === 0) {
      const config = useRuntimeConfig()
      // Check if we have valid Firebase credentials
      if (!config.firebaseProjectId || !config.firebaseClientEmail || !config.firebasePrivateKey) {
        console.warn('Firebase credentials not configured. Using mock database for development.')
        return { db: createMockDatabase() }
      }

      // Initialize with service account credentials
      const app = initializeApp({
        credential: cert({
          projectId: config.firebaseProjectId,
          clientEmail: config.firebaseClientEmail,
          privateKey: config.firebasePrivateKey?.replace(/\\n/g, '\n')
        })
      })

      return { db: getFirestore(app), app }
    } else {
      const app = getApps()[0]
      if (!app) {
        console.warn('No Firebase app found. Using mock database for development.')
        return { db: createMockDatabase() }
      }
      return { db: getFirestore(app), app }
    }
  } catch (error) {
    console.error('Firebase initialization error:', error)
    console.warn('Using mock database for development.')
    return { db: createMockDatabase() }
  }
}

// Initialize and export
const { db, app } = initializeFirebase()

export { db, app }
