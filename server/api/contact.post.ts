import { db } from '../utils/firebase'
import { sendContactEmailNew } from '../utils/email'

interface ContactFormData {
  name: string
  email: string
  message: string
}

// Input validation and sanitization
function validateAndSanitizeInput(data: unknown): ContactFormData {
  // Type guard to check if data is an object
  if (!data || typeof data !== 'object') {
    throw createError({
      statusCode: 400,
      statusMessage: 'Invalid request body'
    })
  }

  const requestData = data as Record<string, unknown>

  // Check if all required fields are present
  if (!requestData.name || !requestData.email || !requestData.message) {
    throw createError({
      statusCode: 400,
      statusMessage: 'Missing required fields: name, email, and message are required'
    })
  }

  // Validate email format
  const emailRegex = /^[^\s@]+@[^\s@]+\.[^\s@]+$/
  if (!emailRegex.test(String(requestData.email))) {
    throw createError({
      statusCode: 400,
      statusMessage: 'Invalid email format'
    })
  }

  // Sanitize inputs (basic HTML escaping and trimming)
  const sanitizedData: ContactFormData = {
    name: String(requestData.name).trim().substring(0, 100), // Limit name to 100 chars
    email: String(requestData.email).trim().toLowerCase().substring(0, 254), // Standard email length limit
    message: String(requestData.message).trim().substring(0, 2000) // Limit message to 2000 chars
  }

  // Additional validation
  if (sanitizedData.name.length < 2) {
    throw createError({
      statusCode: 400,
      statusMessage: 'Name must be at least 2 characters long'
    })
  }

  if (sanitizedData.message.length < 10) {
    throw createError({
      statusCode: 400,
      statusMessage: 'Message must be at least 10 characters long'
    })
  }

  return sanitizedData
}

export default defineEventHandler(async (event) => {
  // Only allow POST requests
  assertMethod(event, 'POST')

  try {
    // Parse request body
    const body = await readBody(event)

    // Validate and sanitize input
    const contactData = validateAndSanitizeInput(body)

    // Save to Firestore
    const docRef = await db.collection('contactMessages').add({
      ...contactData,
      timestamp: new Date(),
      serverTimestamp: new Date().toISOString(),
      ip: getHeader(event, 'x-forwarded-for') || getHeader(event, 'x-real-ip') || 'unknown',
      userAgent: getHeader(event, 'user-agent') || 'Unknown'
    })

    console.log('Contact message saved to Firestore with ID:', docRef.id)

    // Send email notification
    try {
      await sendContactEmailNew(contactData)
      console.log('Email notification sent successfully')
    } catch (emailError) {
      console.error('Email sending failed:', emailError)
      // Don't fail the entire request if email fails
      // The message is still saved to Firestore
    }

    // Return success response
    return {
      success: true,
      message: 'Contact form submitted successfully',
      id: docRef.id
    }
  } catch (error: unknown) {
    console.error('Contact form submission error:', error)

    // Handle specific error types
    if (error && typeof error === 'object' && 'statusCode' in error) {
      // Re-throw validation errors (H3 errors)
      throw error
    }

    // Handle Firebase errors
    if (error && typeof error === 'object' && 'code' in error) {
      const firebaseError = error as { code: string }
      if (typeof firebaseError.code === 'string' && firebaseError.code.startsWith('firestore/')) {
        throw createError({
          statusCode: 500,
          statusMessage: 'Database error - please try again later'
        })
      }
    }

    // Generic server error
    throw createError({
      statusCode: 500,
      statusMessage: 'Internal server error - please try again later'
    })
  }
})
