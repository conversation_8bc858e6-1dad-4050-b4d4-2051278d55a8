# Contact Form API Documentation

## Overview

The contact form API endpoint handles form submissions from the Daily Bread Media website contact form. It validates input, saves messages to Firebase Firestore, and sends email notifications.

## Endpoint

**POST** `/api/contact`

## Request Body

```json
{
  "name": "string (required, 2-100 characters)",
  "email": "string (required, valid email format, max 254 characters)",
  "message": "string (required, 10-2000 characters)"
}
```

## Response

### Success (200)
```json
{
  "success": true,
  "message": "Contact form submitted successfully",
  "id": "firestore-document-id"
}
```

### Validation Error (400)
```json
{
  "error": true,
  "statusCode": 400,
  "statusMessage": "Error message",
  "message": "Detailed error description"
}
```

### Server Error (500)
```json
{
  "error": true,
  "statusCode": 500,
  "statusMessage": "Internal server error - please try again later"
}
```

## Features

### Input Validation
- **Name**: 2-100 characters, trimmed
- **Email**: Valid email format, max 254 characters, converted to lowercase
- **Message**: 10-2000 characters, trimmed
- All inputs are sanitized to prevent XSS

### Data Storage
- Saves to Firebase Firestore collection: `contactMessages`
- Includes timestamp, IP address, and user agent
- Uses server-side timestamp for consistency

### Email Notifications
- **To**: <EMAIL>
- **BCC**: <EMAIL>, <EMAIL>, <EMAIL>
- **Subject**: "New Contact Message from {name}"
- **Reply-To**: Set to sender's email for easy response
- HTML formatted email with contact details and message

### Security Features
- Server-side only (no client access to Firebase credentials)
- Input sanitization and validation
- Rate limiting through Nuxt/Nitro
- Secure email credentials handling

## Environment Variables

Required environment variables (see `.env.example`):

```bash
# Firebase Configuration
NUXT_FIREBASE_PROJECT_ID=your-firebase-project-id
NUXT_FIREBASE_CLIENT_EMAIL=<EMAIL>
NUXT_FIREBASE_PRIVATE_KEY="-----BEGIN PRIVATE KEY-----\nYour private key here\n-----END PRIVATE KEY-----\n"

# Email Configuration
NUXT_EMAIL_HOST=smtp.zoho.com
NUXT_EMAIL_PORT=587
NUXT_EMAIL_USER=<EMAIL>
NUXT_EMAIL_PASSWORD=your-email-password
```

## Development Mode

When Firebase or email credentials are not configured, the API will:
- Use mock Firebase database (logs data to console)
- Skip email sending (logs email data to console)
- Still return success responses for testing

## Error Handling

The API handles various error scenarios:
- Missing or invalid input fields
- Firebase connection issues
- Email sending failures (doesn't fail the entire request)
- Generic server errors

## Testing

Test the API endpoint using curl:

```bash
# Valid request
curl -X POST http://localhost:3001/api/contact \
  -H "Content-Type: application/json" \
  -d '{"name": "Test User", "email": "<EMAIL>", "message": "This is a test message."}'

# Invalid email
curl -X POST http://localhost:3001/api/contact \
  -H "Content-Type: application/json" \
  -d '{"name": "Test", "email": "invalid-email", "message": "Test message"}'
```
