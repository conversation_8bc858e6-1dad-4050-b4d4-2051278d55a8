<script setup lang="ts">
// Home page with Hero Section
useSeoMeta({
  title: 'Websites for Asian Eateries - Daily Bread Media',
  description: 'Professional websites for Asian restaurants starting from $50/month. Includes Facebook & Instagram sync. Get your restaurant online today.'
})

const route = useRoute()

// Handle scrolling to anchor on page load
onMounted(() => {
  // Wait for all components to be mounted and rendered
  nextTick(() => {
    setTimeout(() => {
      // Check if there's a hash in the URL
      if (route.hash) {
        const targetId = route.hash.substring(1) // Remove the # symbol
        const targetElement = document.getElementById(targetId)

        if (targetElement) {
          // Scroll to the target element with smooth behavior
          targetElement.scrollIntoView({
            behavior: 'smooth',
            block: 'start'
          })
        }
      }
    }, 600) // Wait a bit longer to ensure all components are fully rendered
  })
})
</script>

<template>
  <div>
    <HeroSection />
    <WhatWeDo />
    <YourWebsiteSection />
    <HowItWorks />
    <PricingSection />
    <FAQSection />
    <AboutUsSection />
    <ContactSection />
  </div>
</template>
