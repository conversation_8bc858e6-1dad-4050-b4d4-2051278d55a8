<script setup lang="ts">
// Individual FAQ item component with collapsible functionality
interface Props {
  question: string
  answer: string
}

defineProps<Props>()
</script>

<template>
  <details class="faq-item group border-b border-gray-200 last:border-b-0">
    <summary class="faq-question flex cursor-pointer items-center justify-between py-6 px-6 text-left transition-all duration-200 hover:bg-gray-50 focus:outline-none focus:ring-2 focus:ring-orange-500 focus:ring-inset">
      <h3 class="font-satoshi text-lg font-semibold text-gray-900 sm:text-xl">
        {{ question }}
      </h3>
      <div class="faq-icon ml-6 flex-shrink-0 transition-transform duration-200 group-open:rotate-45">
        <svg
          class="h-6 w-6 text-gray-500"
          fill="none"
          viewBox="0 0 24 24"
          stroke="currentColor"
          stroke-width="2"
        >
          <path
            stroke-linecap="round"
            stroke-linejoin="round"
            d="M12 6v6m0 0v6m0-6h6m-6 0H6"
          />
        </svg>
      </div>
    </summary>
    <div class="faq-answer overflow-hidden">
      <div class="px-6 pb-6">
        <p class="font-inter text-gray-600 leading-relaxed">
          {{ answer }}
        </p>
      </div>
    </div>
  </details>
</template>

<style scoped>
/* Remove default details styling */
.faq-item {
  list-style: none;
}

.faq-item::-webkit-details-marker {
  display: none;
}

/* Smooth animation for opening/closing */
.faq-item[open] .faq-answer {
  animation: slideDown 0.3s ease-out;
}

.faq-item:not([open]) .faq-answer {
  animation: slideUp 0.3s ease-out;
}

@keyframes slideDown {
  from {
    opacity: 0;
    max-height: 0;
    padding-bottom: 0;
  }
  to {
    opacity: 1;
    max-height: 200px;
    padding-bottom: 1.5rem;
  }
}

@keyframes slideUp {
  from {
    opacity: 1;
    max-height: 200px;
    padding-bottom: 1.5rem;
  }
  to {
    opacity: 0;
    max-height: 0;
    padding-bottom: 0;
  }
}

/* Hover effects */
.faq-question:hover {
  background-color: #F9FAFB;
}

.faq-question:hover .faq-icon {
  color: #EA580C;
}

/* Focus states for accessibility */
.faq-question:focus {
  background-color: #FFF7ED;
}

/* Icon rotation when open */
.faq-item[open] .faq-icon {
  transform: rotate(45deg);
  color: #EA580C;
}
</style>
