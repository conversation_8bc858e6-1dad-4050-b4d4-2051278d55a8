<script setup lang="ts">
// What We Do section component
</script>

<template>
  <section
    id="what-we-do"
    class="relative overflow-hidden py-24 sm:py-32"
    style="background-color: #FDF4EA;"
  >
    <div class="mx-auto max-w-7xl px-6 lg:px-8">
      <!-- Section Header -->
      <div class="mx-auto max-w-3xl text-center">
        <h2 class="font-satoshi text-3xl font-bold tracking-tight text-gray-900 sm:text-4xl lg:text-5xl">
          One Platform to Manage Your Restaurant's Online Presence
        </h2>
        <p class="font-inter mt-6 text-lg leading-8 text-gray-600 sm:text-xl">
          We specialize in creating beautiful, mobile-ready websites for Asian restaurants and cafés across Australia. Our platform seamlessly integrates your website with social media, helping you reach more customers and grow your business online.
        </p>
      </div>

      <!-- Features Grid -->
      <div class="mx-auto mt-16 max-w-6xl">
        <div class="grid grid-cols-1 gap-8 sm:grid-cols-2 lg:grid-cols-3">
          <WhatWeDoCard
            icon-src="/icons/website_builder_icon.svg"
            title="Website Builder"
            description="We build beautiful, mobile-ready sites customers find on Google."
          />
          <WhatWeDoCard
            icon-src="/icons/social_sync_icon.svg"
            title="Social Sync"
            description="Post once to update your website, Facebook & Instagram together."
          />
          <WhatWeDoCard
            icon-src="/icons/hospitality_icon.svg"
            title="Built for Hospitality"
            description="Designed specifically for Asian restaurants and cafés in Australia."
          />
        </div>
      </div>
    </div>

    <!-- SVG Divider -->
    <div class="absolute bottom-0 left-0 w-full overflow-hidden line-height-0">
      <svg
        class="relative block w-full h-16 sm:h-20 lg:h-24"
        viewBox="0 0 1200 120"
        preserveAspectRatio="none"
        xmlns="http://www.w3.org/2000/svg"
      >
        <path
          d="M985.66,92.83C906.67,72,823.78,31,743.84,14.19c-82.26-17.34-168.06-16.33-250.45.39-57.84,11.73-114,31.07-172,41.86A600.21,600.21,0,0,1,0,27.35V120H1200V95.8C1132.19,118.92,1055.71,111.31,985.66,92.83Z"
          fill="#FFFDF9"
        />
      </svg>
    </div>
  </section>
</template>

<style scoped>
/* Additional custom styles if needed */
</style>
