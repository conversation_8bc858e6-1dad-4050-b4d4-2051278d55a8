<script setup lang="ts">
// Hero Section component for the landing page
</script>

<template>
  <section
    class="relative overflow-hidden"
    style="background-color: #FCFBFA;"
  >
    <!-- Background decoration -->
    <div class="absolute inset-0 bg-gradient-to-br from-gray-50 to-gray-100 opacity-30" />

    <!-- Background pattern -->
    <div class="absolute inset-0 opacity-50">
      <div class="h-full w-full bg-gray-100 bg-[radial-gradient(#e5e7eb_1px,transparent_1px)] [background-size:16px_16px]" />
    </div>

    <div class="relative mx-auto max-w-7xl px-6 py-24 sm:py-32 lg:px-8">
      <div class="mx-auto max-w-4xl text-center">
        <!-- Main headline -->
        <h1 class="font-satoshi text-5xl font-bold tracking-tight text-gray-900 sm:text-7xl lg:text-8xl">
          Affordable Websites for Asian Restaurants — From $50/Month
        </h1>

        <!-- Subheading -->
        <p class="font-inter mt-8 text-lg leading-8 text-gray-500 sm:text-xl">
          Grow your local restaurant with a modern website that updates Facebook & Instagram automatically. Simple. Smart. Deliciously easy.
        </p>

        <!-- CTA Button -->
        <div class="mt-12 flex items-center justify-center">
          <UButton
            to="#pricing"
            size="xl"
            color="secondary"
            variant="solid"
            class="font-satoshi px-12 py-4 text-lg font-semibold rounded-full"
          >
            See Plans
          </UButton>
        </div>
      </div>

      <!-- Hero Image -->
      <div class="mt-16 flow-root sm:mt-24">
        <div class="relative -m-2 rounded-xl bg-gray-900/5 p-2 ring-1 ring-inset ring-gray-900/10 lg:-m-4 lg:rounded-2xl lg:p-4">
          <NuxtImg
            src="/images/hero.png"
            alt="Hero illustration"
            class="w-full rounded-md shadow-2xl ring-1 ring-gray-900/10"
            width="2432"
            height="1442"
            loading="eager"
            format="webp"
          />
        </div>
      </div>
    </div>

    <!-- SVG Divider -->
    <div class="absolute bottom-0 left-0 w-full overflow-hidden line-height-0">
      <svg
        class="relative block w-full h-16 sm:h-20 lg:h-24"
        viewBox="0 0 1200 120"
        preserveAspectRatio="none"
        xmlns="http://www.w3.org/2000/svg"
      >
        <path
          d="M0,0V46.29c47.79,22.2,103.59,32.17,158,28,70.36-5.37,136.33-33.31,206.8-37.5C438.64,32.43,512.34,53.67,583,72.05c69.27,18,138.3,24.88,209.4,13.08,36.15-6,69.85-17.84,104.45-29.34C989.49,25,1113-14.29,1200,52.47V120H0Z"
          fill="#FDF4EA"
        />
      </svg>
    </div>
  </section>
</template>

<style scoped>
/* Additional custom styles if needed */
</style>
