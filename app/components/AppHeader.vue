<script setup lang="ts">
const { activeHeadings, updateHeadings } = useCustomScrollspy()
const route = useRoute()

// Check if we're on the home page
const isHomePage = computed(() => route.path === '/')

const items = computed(() => [{
  label: 'How it works?',
  to: isHomePage.value ? '#how-it-works' : '/#how-it-works',
  active: activeHeadings.value.includes('how-it-works')
}, {
  label: 'Pricing',
  to: isHomePage.value ? '#pricing' : '/#pricing',
  active: activeHeadings.value.includes('pricing')
}, {
  label: 'FAQs',
  to: isHomePage.value ? '#faq' : '/#faq',
  active: activeHeadings.value.includes('faq')
}, {
  label: 'About Us',
  to: isHomePage.value ? '#about-us' : '/#about-us',
  active: activeHeadings.value.includes('about-us')
}, {
  label: 'Contact',
  to: isHomePage.value ? '#contact' : '/#contact',
  active: activeHeadings.value.includes('contact')
}])

// Initialize scrollspy when page is ready
onMounted(() => {
  // Use nextTick to ensure DOM is fully rendered
  nextTick(() => {
    // Add a small delay to ensure all components are mounted
    setTimeout(() => {
      const elements = [
        document.querySelector('#how-it-works'),
        document.querySelector('#pricing'),
        document.querySelector('#faq'),
        document.querySelector('#about-us'),
        document.querySelector('#contact')
      ].filter(Boolean) as Element[]

      if (elements.length > 0) {
        updateHeadings(elements)
      }
    }, 500)
  })
})
</script>

<template>
  <UHeader
    color="white"
    class="bg-white border-b border-gray-200"
  >
    <template #left>
      <NuxtLink to="/">
        <LogoPro />
      </NuxtLink>
    </template>

    <template #right>
      <UNavigationMenu
        :items="items"
        variant="link"
        class="font-satoshi hidden lg:block"
      />
    </template>

    <template #body>
      <UNavigationMenu
        :items="items"
        orientation="vertical"
        class="font-satoshi -mx-2.5 mobile-nav-menu"
      />
    </template>
  </UHeader>
</template>
