<script setup lang="ts">
// Your Website is the New Front Door section component
</script>

<template>
  <section
    id="your-website-section"
    class="relative overflow-hidden py-24 sm:py-32"
    style="background-color: #FFFDF9;"
  >
    <div class="mx-auto max-w-7xl px-6 lg:px-8">
      <!-- Section Header -->
      <div class="mx-auto max-w-4xl text-center">
        <h2 class="font-satoshi text-3xl font-bold tracking-tight text-gray-900 sm:text-4xl lg:text-5xl">
          Your Website is the New Front Door
        </h2>

        <!-- Large Statistic -->
        <div class="mt-8 mb-8">
          <div class="inline-flex items-center justify-center">
            <span class="font-satoshi text-6xl sm:text-7xl lg:text-8xl font-bold text-orange-600">77%</span>
          </div>
        </div>

        <!-- Main Paragraph -->
        <div class="mx-auto max-w-3xl">
          <p class="font-inter text-lg leading-8 text-gray-600 sm:text-xl mb-6">
            <strong>77% of customers check a restaurant's website</strong> before ordering or visiting. But many restaurants rely only on Instagram or Facebook, making it harder for customers to find opening hours, menus, and contact details.
          </p>

          <p class="font-inter text-lg leading-8 text-gray-600 sm:text-xl mb-8">
            Daily Bread Media solves that by giving you a fast, <span class="text-orange-600 font-medium">mobile website for takeaway shops</span> that:
          </p>
        </div>
      </div>

      <!-- Benefits Checklist -->
      <div class="mx-auto mt-12 max-w-2xl">
        <div class="space-y-6">
          <YourWebsiteBenefit
            title="Shows up on Google"
            description="Get found by customers searching for restaurant marketing Australia and local dining options"
          />
          <YourWebsiteBenefit
            title="Loads fast on mobile"
            description="Optimized for mobile devices where 80% of restaurant searches happen"
          />
          <YourWebsiteBenefit
            title="Syncs with your social media"
            description="Learn how to get found on Google Maps while keeping your Instagram and Facebook updated"
          />
        </div>
      </div>

      <!-- Optional Mobile Phone Visual -->
      <div class="mx-auto mt-16 max-w-md text-center">
        <div class="relative">
          <!-- Mobile Phone Frame -->
          <div class="mx-auto w-64 h-96 bg-gray-900 rounded-3xl p-2 shadow-2xl">
            <div class="w-full h-full bg-white rounded-2xl overflow-hidden relative">
              <!-- Phone Screen Content -->
              <div class="p-4 space-y-3">
                <div class="h-4 bg-orange-100 rounded"></div>
                <div class="h-8 bg-orange-200 rounded"></div>
                <div class="space-y-2">
                  <div class="h-3 bg-gray-100 rounded"></div>
                  <div class="h-3 bg-gray-100 rounded w-3/4"></div>
                  <div class="h-3 bg-gray-100 rounded w-1/2"></div>
                </div>
                <div class="grid grid-cols-2 gap-2 mt-4">
                  <div class="h-16 bg-orange-50 rounded"></div>
                  <div class="h-16 bg-orange-50 rounded"></div>
                </div>
              </div>

              <!-- Menu indicator -->
              <div class="absolute bottom-4 left-4 right-4">
                <div class="text-xs text-gray-500 text-center font-inter">
                  Mobile Menu Preview
                </div>
              </div>
            </div>
          </div>

          <!-- Floating elements -->
          <div class="absolute -top-4 -right-4 w-8 h-8 bg-green-500 rounded-full flex items-center justify-center">
            <svg class="w-4 h-4 text-white" fill="currentColor" viewBox="0 0 20 20">
              <path fill-rule="evenodd" d="M16.707 5.293a1 1 0 010 1.414l-8 8a1 1 0 01-1.414 0l-4-4a1 1 0 011.414-1.414L8 12.586l7.293-7.293a1 1 0 011.414 0z" clip-rule="evenodd"></path>
            </svg>
          </div>

          <div class="absolute -bottom-4 -left-4 w-8 h-8 bg-blue-500 rounded-full flex items-center justify-center">
            <svg class="w-4 h-4 text-white" fill="currentColor" viewBox="0 0 20 20">
              <path d="M9 12l2 2 4-4m6 2a9 9 0 11-18 0 9 9 0 0118 0z"></path>
            </svg>
          </div>
        </div>
      </div>
    </div>

    <!-- SVG Divider -->
    <div class="absolute bottom-0 left-0 w-full overflow-hidden line-height-0">
      <svg
        class="relative block w-full h-16 sm:h-20 lg:h-24"
        viewBox="0 0 1200 120"
        preserveAspectRatio="none"
        xmlns="http://www.w3.org/2000/svg"
      >
        <path
          d="M0,0V46.29c47.79,22.2,103.59,32.17,158,28,70.36-5.37,136.33-33.31,206.8-37.5C438.64,32.43,512.34,53.67,583,72.05c69.27,18,138.3,24.88,209.4,13.08,36.15-6,69.85-17.84,104.45-29.34C989.49,25,1113-14.29,1200,52.47V120H0Z"
          fill="#FFF8F0"
        />
      </svg>
    </div>
  </section>
</template>

<style scoped>
/* Additional custom styles if needed */
</style>
