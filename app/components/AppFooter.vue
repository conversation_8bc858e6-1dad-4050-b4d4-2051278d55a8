<script setup lang="ts">
// Footer component with navigation links
const route = useRoute()

// Check if we're on the home page
const isHomePage = computed(() => route.path === '/')

const footerColumns = computed(() => [
  {
    title: 'Daily Bread Media',
    links: [
      { label: 'What We Do', href: isHomePage.value ? '#what-we-do' : '/#what-we-do' },
      { label: 'How It Works', href: isHomePage.value ? '#how-it-works' : '/#how-it-works' },
      { label: 'Pricing', href: isHomePage.value ? '#pricing' : '/#pricing' },
      { label: 'Contact', href: isHomePage.value ? '#contact' : '/#contact' }
    ]
  },
  {
    title: 'Resources',
    links: [
      { label: 'Your Website Benefits', href: isHomePage.value ? '#your-website-section' : '/#your-website-section' },
      { label: 'FAQs', href: isHomePage.value ? '#faq' : '/#faq' },
      { label: 'Support', href: '#' },
      { label: 'Blog', href: '#' }
    ]
  },
  {
    title: 'Company',
    links: [
      { label: 'About Us', href: isHomePage.value ? '#about-us' : '/#about-us' },
      { label: 'Terms', href: '/terms' },
      { label: 'Privacy', href: '/privacy' }
    ]
  }
])
</script>

<template>
  <footer
    class="border-t border-gray-200"
    style="background-color: #F9F9F9;"
  >
    <div class="mx-auto max-w-7xl px-6 py-16 lg:px-8">
      <!-- Logo and Navigation -->
      <div class="grid grid-cols-1 gap-8 lg:grid-cols-4">
        <!-- Logo Column -->
        <div class="lg:col-span-1">
          <LogoPro class="w-auto h-8 mb-4" />
          <p class="font-inter text-sm text-gray-600">
            ABN **************
          </p>
        </div>

        <!-- Navigation Columns -->
        <div class="lg:col-span-3">
          <div class="grid grid-cols-1 gap-8 sm:grid-cols-3">
            <div
              v-for="column in footerColumns"
              :key="column.title"
              class="space-y-4"
            >
              <h3 class="font-satoshi text-sm font-semibold text-gray-900">
                {{ column.title }}
              </h3>
              <ul class="space-y-3">
                <li
                  v-for="link in column.links"
                  :key="link.label"
                >
                  <a
                    :href="link.href"
                    class="font-inter text-sm text-gray-600 hover:text-gray-900 transition-colors duration-200"
                  >
                    {{ link.label }}
                  </a>
                </li>
              </ul>
            </div>
          </div>
        </div>
      </div>

      <!-- Copyright -->
      <div class="mt-12 pt-8 border-t border-gray-200">
        <p class="font-inter text-sm text-gray-500">
          © 2025 Daily Bread Media
        </p>
      </div>
    </div>
  </footer>
</template>
