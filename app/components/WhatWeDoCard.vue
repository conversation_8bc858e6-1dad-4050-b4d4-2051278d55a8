<script setup lang="ts">
// Reusable feature card component for What We Do section
interface Props {
  iconSrc: string
  title: string
  description: string
}

defineProps<Props>()
</script>

<template>
  <div class="feature-card group relative flex flex-col items-center text-center p-8 rounded-xl shadow-lg bg-white transition-all duration-300 ease-in-out transform hover:-translate-y-2 hover:shadow-xl">
    <!-- Icon -->
    <div class="flex h-16 w-16 items-center justify-center rounded-full bg-orange-100 text-orange-600 mb-6 group-hover:bg-orange-200 transition-colors duration-300">
      <img
        :src="iconSrc"
        :alt="title + ' icon'"
        class="h-8 w-8"
      >
    </div>

    <!-- Title -->
    <h3 class="font-satoshi text-xl font-semibold text-gray-900 mb-4">
      {{ title }}
    </h3>

    <!-- Description -->
    <p class="font-inter text-gray-600 leading-relaxed">
      {{ description }}
    </p>
  </div>
</template>

<style scoped>
.feature-card:hover {
  background-color: #FEFEFE;
}
</style>
