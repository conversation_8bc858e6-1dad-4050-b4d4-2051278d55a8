<script setup lang="ts">
// Reusable step card component
interface Props {
  stepNumber: number
  image: string
  title: string
  description: string
}

defineProps<Props>()
</script>

<template>
  <div
    class="step-card relative flex flex-col rounded-xl shadow-lg overflow-hidden transition-all duration-300 ease-in-out transform hover:-translate-y-2 hover:shadow-xl bg-white"
  >
    <!-- Image - 300x300 -->
    <div class="w-full">
      <img
        :src="image"
        :alt="title"
        class="w-full h-[300px] object-cover"
      >
    </div>

    <!-- Content -->
    <div class="flex flex-col items-center text-center p-6 sm:p-8">
      <!-- Step Number Badge -->
      <div class="flex h-14 w-14 items-center justify-center rounded-full bg-orange-500 text-white font-satoshi text-xl font-bold mb-6 shadow-lg">
        {{ stepNumber }}
      </div>

      <!-- Title -->
      <h3 class="font-satoshi text-xl font-bold text-gray-900 mb-4 sm:text-2xl">
        {{ title }}
      </h3>

      <!-- Description -->
      <p class="font-inter text-gray-600 leading-relaxed text-base">
        {{ description }}
      </p>
    </div>
  </div>
</template>

<style scoped>
/* Hover effects for cards */
.step-card {
  transition: all 0.3s ease-in-out;
}

.step-card:hover {
  transform: translateY(-8px);
  box-shadow: 0 25px 50px -12px rgba(0, 0, 0, 0.15);
}

/* Ensure consistent card height */
.step-card {
  height: 100%;
  min-height: 500px;
}

/* Image container styling */
.step-card img {
  transition: transform 0.3s ease-in-out;
}

.step-card:hover img {
  transform: scale(1.02);
}
</style>
