<script setup lang="ts">
// Pricing section with multiple plans
</script>

<template>
  <section
    id="pricing"
    class="relative overflow-hidden bg-white py-24 sm:py-32"
  >
    <div class="mx-auto max-w-7xl px-6 lg:px-8">
      <!-- Section Header -->
      <div class="mx-auto max-w-2xl text-center">
        <h2 class="font-satoshi text-3xl font-bold tracking-tight text-gray-900 sm:text-4xl lg:text-5xl">
          Plans for Every Restaurant
        </h2>
        <p class="font-inter mt-6 text-lg leading-8 text-gray-600">
          Simple. Affordable. Flexible.
        </p>
      </div>

      <!-- Pricing Grid -->
      <div class="mx-auto mt-16 max-w-4xl">
        <div class="grid grid-cols-1 gap-8 sm:grid-cols-2 pt-4">
          <PricingCard
            plan-name="Starter"
            price="$50"
            :features="[
              'Mobile website',
              'Menu & hours update',
              'Email support'
            ]"
            button-text="Choose Starter"
            button-variant="solid"
            :is-popular="true"
          />
          <PricingCard
            plan-name="Growth"
            price="$100"
            :features="[
              'Plus social syncing',
              'Auto Instagram posts',
              'Priority support'
            ]"
            button-text="Choose Growth"
            button-variant="outline"
          />
        </div>
      </div>
    </div>

    <!-- SVG Divider -->
    <div class="absolute bottom-0 left-0 w-full overflow-hidden line-height-0">
      <svg
        class="relative block w-full h-16 sm:h-20 lg:h-24"
        viewBox="0 0 1200 120"
        preserveAspectRatio="none"
        xmlns="http://www.w3.org/2000/svg"
      >
        <path
          d="M0,0V46.29c47.79,22.2,103.59,32.17,158,28,70.36-5.37,136.33-33.31,206.8-37.5C438.64,32.43,512.34,53.67,583,72.05c69.27,18,138.3,24.88,209.4,13.08,36.15-6,69.85-17.84,104.45-29.34C989.49,25,1113-14.29,1200,52.47V120H0Z"
          fill="#FAFAFA"
        />
      </svg>
    </div>
  </section>
</template>

<style scoped>
/* Additional custom styles if needed */
</style>
