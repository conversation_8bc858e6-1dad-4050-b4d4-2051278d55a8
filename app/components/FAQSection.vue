<script setup lang="ts">
// FAQ section component with collapsible questions and answers
const faqs = [
  {
    question: 'How quickly can you launch my website?',
    answer: 'We can launch most restaurant websites in under 7 days using your Instagram or menu content.'
  },
  {
    question: 'Do I need to know how to code?',
    answer: 'Not at all. Everything is handled by us and managed through a simple dashboard. You\'ll never touch a line of code.'
  },
  {
    question: 'Can I use my existing Instagram photos?',
    answer: 'Yes! We can pull content from your existing Instagram and Facebook pages to help build your site faster.'
  },
  {
    question: 'Can I connect my own domain or use yours?',
    answer: 'You can bring your own domain name or use one of ours for free. We\'ll guide you through it.'
  },
  {
    question: 'What if I want to cancel?',
    answer: 'No lock-in contracts. You can cancel anytime with just 7 days\' notice.'
  },
  {
    question: 'Can I update my menu or hours myself?',
    answer: 'Absolutely. You\'ll get access to a simple CMS where you can update your menu, hours, and special offers anytime.'
  }
]
</script>

<template>
  <section
    id="faq"
    class="relative overflow-hidden py-24 sm:py-32"
    style="background-color: #FAFAFA;"
  >
    <div class="mx-auto max-w-7xl px-6 lg:px-8">
      <!-- Section Header -->
      <div class="mx-auto max-w-3xl text-center">
        <h2 class="font-satoshi text-3xl font-bold tracking-tight text-gray-900 sm:text-4xl lg:text-5xl">
          Frequently Asked Questions
        </h2>
        <p class="font-inter mt-6 text-lg leading-8 text-gray-600 sm:text-xl">
          Everything you need to know about getting your restaurant online with us.
        </p>
      </div>

      <!-- FAQ List -->
      <div class="mx-auto mt-16 max-w-4xl">
        <div class="faq-container rounded-xl bg-white shadow-lg overflow-hidden">
          <FAQItem
            v-for="(faq, index) in faqs"
            :key="index"
            :question="faq.question"
            :answer="faq.answer"
          />
        </div>
      </div>

      <!-- Additional CTA -->
      <div class="mx-auto mt-12 max-w-2xl text-center">
        <p class="font-inter text-gray-600 mb-6">
          Still have questions? We're here to help.
        </p>
        <UButton
          to="#contact"
          size="lg"
          color="secondary"
          variant="outline"
          class="font-satoshi px-8 py-3 text-base font-semibold rounded-full"
        >
          Contact Us
        </UButton>
      </div>
    </div>

    <!-- SVG Divider -->
    <div class="absolute bottom-0 left-0 w-full overflow-hidden line-height-0">
      <svg
        class="relative block w-full h-16 sm:h-20 lg:h-24"
        viewBox="0 0 1200 120"
        preserveAspectRatio="none"
        xmlns="http://www.w3.org/2000/svg"
      >
        <path
          d="M985.66,92.83C906.67,72,823.78,31,743.84,14.19c-82.26-17.34-168.06-16.33-250.45.39-57.84,11.73-114,31.07-172,41.86A600.21,600.21,0,0,1,0,27.35V120H1200V95.8C1132.19,118.92,1055.71,111.31,985.66,92.83Z"
          fill="#FDF4EA"
        />
      </svg>
    </div>
  </section>
</template>

<style scoped>
/* Container styling */
.faq-container {
  border: 1px solid #E5E7EB;
}

/* Subtle shadow and hover effects */
.faq-container:hover {
  box-shadow: 0 20px 25px -5px rgba(0, 0, 0, 0.1), 0 10px 10px -5px rgba(0, 0, 0, 0.04);
}

/* Smooth transition for container */
.faq-container {
  transition: box-shadow 0.3s ease-in-out;
}
</style>
