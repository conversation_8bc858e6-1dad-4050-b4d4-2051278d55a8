<script setup lang="ts">
// Reusable benefit item component for Your Website section
interface Props {
  title: string
  description: string
}

defineProps<Props>()
</script>

<template>
  <div class="flex items-start space-x-4 p-4 rounded-lg bg-white/50 hover:bg-white/80 transition-colors duration-300">
    <!-- Checkmark Icon -->
    <div class="flex-shrink-0 mt-1">
      <div class="flex h-8 w-8 items-center justify-center rounded-full bg-green-100 text-green-600">
        <svg
          class="h-5 w-5"
          fill="currentColor"
          viewBox="0 0 20 20"
          xmlns="http://www.w3.org/2000/svg"
        >
          <path
            fill-rule="evenodd"
            d="M16.707 5.293a1 1 0 010 1.414l-8 8a1 1 0 01-1.414 0l-4-4a1 1 0 011.414-1.414L8 12.586l7.293-7.293a1 1 0 011.414 0z"
            clip-rule="evenodd"
          />
        </svg>
      </div>
    </div>

    <!-- Content -->
    <div class="flex-1">
      <h3 class="font-satoshi text-lg font-semibold text-gray-900 mb-2">
        {{ title }}
      </h3>
      <p class="font-inter text-gray-600 leading-relaxed">
        {{ description }}
      </p>
    </div>
  </div>
</template>

<style scoped>
/* Additional custom styles if needed */
</style>
