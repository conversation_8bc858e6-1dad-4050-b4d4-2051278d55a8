<script setup lang="ts">
// About Us section component with team story and values
</script>

<template>
  <section
    id="about-us"
    class="relative overflow-hidden py-24 sm:py-32"
    style="background-color: #FDF4EA;"
  >
    <div class="mx-auto max-w-7xl px-6 lg:px-8">
      <!-- Section Header -->
      <div class="mx-auto max-w-3xl text-center mb-16">
        <h2 class="font-satoshi text-3xl font-bold tracking-tight text-gray-900 sm:text-4xl lg:text-5xl">
          About Daily Bread Media
        </h2>
        <!-- Optional decorative element -->
        <div class="mt-6 flex justify-center">
          <div class="h-1 w-16 bg-orange-400 rounded-full" />
        </div>
      </div>

      <!-- Main Content -->
      <div class="mx-auto max-w-4xl">
        <!-- Text Content -->
        <div class="bg-white rounded-2xl p-8 shadow-lg hover:shadow-xl transition-all duration-300 ease-in-out">
          <!-- Mission Statement -->
          <div class="mb-8">
            <h3 class="font-satoshi text-2xl font-semibold text-gray-900 mb-6">
              Our Story
            </h3>
            <p class="font-inter text-lg leading-relaxed text-gray-700 mb-6">
              We're a team of 3 friends based in Australia with a passion for food, design, and small business growth. Our mission is to help local restaurants — especially Asian eateries — thrive online without spending a fortune.
            </p>
            <p class="font-inter text-lg leading-relaxed text-gray-700">
              Our name is inspired by the idea of daily provision and steady growth, and we're proud to support businesses that nourish their communities.
            </p>
          </div>

          <!-- Values/Mission -->
          <div class="border-t border-gray-200 pt-6">
            <h4 class="font-satoshi text-lg font-semibold text-gray-900 mb-4">
              What Drives Us
            </h4>
            <ul class="space-y-3">
              <li class="flex items-start">
                <svg
                  class="h-5 w-5 text-orange-500 mr-3 mt-0.5 flex-shrink-0"
                  fill="currentColor"
                  viewBox="0 0 20 20"
                >
                  <path
                    fill-rule="evenodd"
                    d="M16.707 5.293a1 1 0 010 1.414l-8 8a1 1 0 01-1.414 0l-4-4a1 1 0 011.414-1.414L8 12.586l7.293-7.293a1 1 0 011.414 0z"
                    clip-rule="evenodd"
                  />
                </svg>
                <span class="font-inter text-gray-600">Supporting local Asian food businesses</span>
              </li>
              <li class="flex items-start">
                <svg
                  class="h-5 w-5 text-orange-500 mr-3 mt-0.5 flex-shrink-0"
                  fill="currentColor"
                  viewBox="0 0 20 20"
                >
                  <path
                    fill-rule="evenodd"
                    d="M16.707 5.293a1 1 0 010 1.414l-8 8a1 1 0 01-1.414 0l-4-4a1 1 0 011.414-1.414L8 12.586l7.293-7.293a1 1 0 011.414 0z"
                    clip-rule="evenodd"
                  />
                </svg>
                <span class="font-inter text-gray-600">Making professional websites affordable</span>
              </li>
              <li class="flex items-start">
                <svg
                  class="h-5 w-5 text-orange-500 mr-3 mt-0.5 flex-shrink-0"
                  fill="currentColor"
                  viewBox="0 0 20 20"
                >
                  <path
                    fill-rule="evenodd"
                    d="M16.707 5.293a1 1 0 010 1.414l-8 8a1 1 0 01-1.414 0l-4-4a1 1 0 011.414-1.414L8 12.586l7.293-7.293a1 1 0 011.414 0z"
                    clip-rule="evenodd"
                  />
                </svg>
                <span class="font-inter text-gray-600">Building community connections through food</span>
              </li>
            </ul>
          </div>
        </div>
      </div>

      <!-- Optional CTA or contact info -->
      <div class="mx-auto mt-16 max-w-2xl text-center">
        <p class="font-inter text-gray-600 mb-6">
          Ready to grow your restaurant's online presence?
        </p>
        <UButton
          to="#contact"
          size="lg"
          color="secondary"
          variant="solid"
          class="font-satoshi px-8 py-3 text-base font-semibold rounded-full"
        >
          Get Started Today
        </UButton>
      </div>
    </div>

    <!-- SVG Divider -->
    <div class="absolute bottom-0 left-0 w-full overflow-hidden line-height-0">
      <svg
        class="relative block w-full h-16 sm:h-20 lg:h-24"
        viewBox="0 0 1200 120"
        preserveAspectRatio="none"
        xmlns="http://www.w3.org/2000/svg"
      >
        <path
          d="M0,0V46.29c47.79,22.2,103.59,32.17,158,28,70.36-5.37,136.33-33.31,206.8-37.5C438.64,32.43,512.34,53.67,583,72.05c69.27,18,138.3,24.88,209.4,13.08,36.15-6,69.85-17.84,104.45-29.34C989.49,25,1113-14.29,1200,52.47V120H0Z"
          fill="#FCFBFA"
        />
      </svg>
    </div>
  </section>
</template>

<style scoped>
/* Smooth transitions for hover effects */
.transition-all {
  transition: all 0.3s ease-in-out;
}

/* Custom shadow effects */
.shadow-lg:hover {
  box-shadow: 0 25px 50px -12px rgba(0, 0, 0, 0.15);
}

/* Subtle animation for decorative elements */
@keyframes float {
  0%, 100% { transform: translateY(0px); }
  50% { transform: translateY(-10px); }
}

.absolute.rounded-full {
  animation: float 6s ease-in-out infinite;
}

.absolute.rounded-full:nth-child(2) {
  animation-delay: -3s;
}
</style>
