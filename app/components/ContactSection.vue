<script setup lang="ts">
// Contact Us form section component
const toast = useToast()

const formData = ref({
  name: '',
  email: '',
  message: ''
})

const loading = ref(false)

async function onSubmit() {
  loading.value = true

  try {
    // Submit form to API endpoint
    const response = await $fetch('/api/contact', {
      method: 'POST',
      body: formData.value
    })

    if (response.success) {
      toast.add({
        title: 'Message Sent!',
        description: 'Thank you for your message. We\'ll get back to you within 24 hours.',
        color: 'success'
      })

      // Reset form
      formData.value = {
        name: '',
        email: '',
        message: ''
      }
    }
  } catch (error: unknown) {
    console.error('Contact form submission error:', error)

    // Show user-friendly error message
    const errorMessage = (error && typeof error === 'object' && 'data' in error &&
                         error.data && typeof error.data === 'object' && 'statusMessage' in error.data)
                         ? String(error.data.statusMessage)
                         : 'Something went wrong. Please try again.'

    toast.add({
      title: 'Error',
      description: errorMessage,
      color: 'error'
    })
  } finally {
    loading.value = false
  }
}
</script>

<template>
  <section
    id="contact"
    class="relative overflow-hidden py-24 sm:py-32"
    style="background-color: #FCFBFA;"
  >
    <div class="mx-auto max-w-7xl px-6 lg:px-8">
      <!-- Section Header -->
      <div class="mx-auto max-w-2xl text-center mb-16">
        <h2 class="font-satoshi text-3xl font-bold tracking-tight text-gray-900 sm:text-4xl lg:text-5xl">
          Contact Us
        </h2>
        <p class="font-inter mt-6 text-lg leading-8 text-gray-600 sm:text-xl">
          Ready to get your restaurant online? Send us a message and we'll get back to you within 24 hours.
        </p>
      </div>

      <!-- Contact Form -->
      <div class="mx-auto max-w-xl">
        <form @submit.prevent="onSubmit" class="space-y-6">
          <!-- Name and Email Row -->
          <div class="grid grid-cols-1 gap-6 sm:grid-cols-2">
            <!-- Name Field -->
            <div>
              <label for="name" class="block text-sm font-medium text-gray-700 mb-2">
                Name
              </label>
              <input
                id="name"
                v-model="formData.name"
                type="text"
                required
                placeholder="Jane Smith"
                class="block w-full rounded-lg border-0 px-4 py-3 text-gray-900 shadow-sm ring-1 ring-inset ring-gray-300 placeholder:text-gray-400 focus:ring-2 focus:ring-inset focus:ring-orange-500 sm:text-sm sm:leading-6 font-inter bg-gray-100"
              />
            </div>

            <!-- Email Field -->
            <div>
              <label for="email" class="block text-sm font-medium text-gray-700 mb-2">
                Email
              </label>
              <input
                id="email"
                v-model="formData.email"
                type="email"
                required
                placeholder="<EMAIL>"
                class="block w-full rounded-lg border-0 px-4 py-3 text-gray-900 shadow-sm ring-1 ring-inset ring-gray-300 placeholder:text-gray-400 focus:ring-2 focus:ring-inset focus:ring-orange-500 sm:text-sm sm:leading-6 font-inter bg-gray-100"
              />
            </div>
          </div>

          <!-- Message Field -->
          <div>
            <label for="message" class="block text-sm font-medium text-gray-700 mb-2">
              Message
            </label>
            <textarea
              id="message"
              v-model="formData.message"
              rows="6"
              required
              placeholder="Your message..."
              class="block w-full rounded-lg border-0 px-4 py-3 text-gray-900 shadow-sm ring-1 ring-inset ring-gray-300 placeholder:text-gray-400 focus:ring-2 focus:ring-inset focus:ring-orange-500 sm:text-sm sm:leading-6 font-inter bg-gray-100 resize-none"
            />
          </div>

          <!-- Submit Button -->
          <div>
            <button
              type="submit"
              :disabled="loading"
              class="w-full flex justify-center items-center rounded-lg bg-black px-6 py-3 text-sm font-semibold text-white shadow-sm hover:bg-gray-800 focus-visible:outline-2 focus-visible:outline-offset-2 focus-visible:outline-black disabled:opacity-50 disabled:cursor-not-allowed transition-colors duration-200 font-satoshi"
            >
              <span v-if="!loading">Submit</span>
              <span v-else class="flex items-center">
                <svg class="animate-spin -ml-1 mr-2 h-4 w-4 text-white" xmlns="http://www.w3.org/2000/svg" fill="none" viewBox="0 0 24 24">
                  <circle class="opacity-25" cx="12" cy="12" r="10" stroke="currentColor" stroke-width="4"></circle>
                  <path class="opacity-75" fill="currentColor" d="M4 12a8 8 0 018-8V0C5.373 0 0 5.373 0 12h4zm2 5.291A7.962 7.962 0 014 12H0c0 3.042 1.135 5.824 3 7.938l3-2.647z"></path>
                </svg>
                Sending...
              </span>
            </button>
          </div>
        </form>
      </div>
    </div>
  </section>
</template>

<style scoped>
/* Custom focus styles for form elements */
input:focus,
textarea:focus {
  outline: none;
}

/* Smooth transitions */
button {
  transition: all 0.2s ease-in-out;
}

/* Form field styling */
input,
textarea {
  transition: all 0.2s ease-in-out;
}

input:hover,
textarea:hover {
  background-color: #F3F4F6;
}
</style>
