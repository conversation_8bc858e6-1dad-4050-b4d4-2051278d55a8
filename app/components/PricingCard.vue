<script setup lang="ts">
// Reusable pricing card component
interface Props {
  planName: string
  price: string
  features: string[]
  buttonText: string
  isPopular?: boolean
  buttonVariant?: 'solid' | 'outline'
}

defineProps<Props>()
</script>

<template>
  <div
    class="pricing-card relative flex flex-col rounded-xl shadow-lg overflow-visible transition-all duration-300 ease-in-out transform hover:-translate-y-1 hover:shadow-xl bg-gray-50 hover:bg-gray-100"
    :class="{ 'ring-2 ring-orange-500': isPopular }"
  >
    <!-- Popular Badge -->
    <div
      v-if="isPopular"
      class="absolute top-0 left-1/2 transform -translate-x-1/2 -translate-y-1/2 bg-orange-500 text-white px-4 py-1 rounded-full text-sm font-satoshi font-semibold z-10"
    >
      Most Popular
    </div>

    <!-- Content -->
    <div class="flex flex-col p-8 h-full">
      <!-- Plan Name -->
      <h3 class="font-satoshi text-xl font-semibold text-gray-900 mb-4">
        {{ planName }}
      </h3>

      <!-- Price -->
      <div class="mb-6">
        <span class="font-satoshi text-4xl font-bold text-gray-900">{{ price }}</span>
        <span class="font-inter text-gray-600 ml-1">/month</span>
      </div>

      <!-- Features -->
      <ul class="space-y-3 mb-8 flex-grow">
        <li
          v-for="feature in features"
          :key="feature"
          class="flex items-start"
        >
          <svg
            class="h-5 w-5 text-green-500 mr-3 mt-0.5 flex-shrink-0"
            fill="currentColor"
            viewBox="0 0 20 20"
          >
            <path
              fill-rule="evenodd"
              d="M16.707 5.293a1 1 0 010 1.414l-8 8a1 1 0 01-1.414 0l-4-4a1 1 0 011.414-1.414L8 12.586l7.293-7.293a1 1 0 011.414 0z"
              clip-rule="evenodd"
            />
          </svg>
          <span class="font-inter text-gray-600">{{ feature }}</span>
        </li>
      </ul>

      <!-- CTA Button -->
      <UButton
        to="#contact"
        :variant="buttonVariant || 'outline'"
        :color="isPopular ? 'primary' : 'neutral'"
        size="lg"
        class="font-satoshi w-full font-semibold"
        :class="{ 'bg-black text-white hover:bg-gray-800': isPopular && buttonVariant === 'solid' }"
      >
        {{ buttonText }}
      </UButton>
    </div>
  </div>
</template>

<style scoped>
.pricing-card {
  min-height: 400px;
}
</style>
