<script setup lang="ts">
// How It Works component with 4 steps
</script>

<template>
  <section
    id="how-it-works"
    class="relative overflow-hidden py-20 sm:py-24"
    style="background-color: #FFF8F0;"
  >
    <div class="mx-auto max-w-7xl px-6 lg:px-8">
      <!-- Section Header -->
      <div class="mx-auto max-w-3xl text-center">
        <h2 class="font-satoshi text-3xl font-bold tracking-tight text-gray-900 sm:text-4xl lg:text-5xl">
          How It Works
        </h2>
        <p class="font-inter mt-6 text-lg leading-8 text-gray-600 sm:text-xl">
          Get your restaurant online with our simple 4-step process. We handle everything so you can focus on what you do best.
        </p>
      </div>

      <!-- Steps Grid -->
      <div class="mx-auto mt-16 max-w-7xl">
        <div class="grid grid-cols-1 gap-8 sm:grid-cols-2 lg:grid-cols-4">
          <HowItWorksStep
            :step-number="1"
            image="/images/Step-1.jpg"
            title="Expression of Interest"
            description="Provide your business details, menu, opening hours, and any reviews or photos. We'll use this to build your first draft."
          />
          <HowItWorksStep
            :step-number="2"
            image="/images/Step-2.jpg"
            title="Development"
            description="Our team will build your mobile-friendly website using your content. It takes about 5–7 days."
          />
          <HowItWorksStep
            :step-number="3"
            image="/images/Step-3.jpg"
            title="Confirmation"
            description="Review the draft and confirm the design and content. We'll make any final changes before going live."
          />
          <HowItWorksStep
            :step-number="4"
            image="/images/Step-4.jpg"
            title="Launch and Deployment"
            description="Once approved, we connect your domain and publish your site. Billing starts after launch."
          />
        </div>
      </div>

      <!-- Call to Action -->
      <div class="mt-16 text-center">
        <UButton
          to="#contact"
          size="xl"
          color="primary"
          variant="solid"
          class="font-satoshi px-8 py-4 text-lg font-semibold rounded-full"
        >
          Get Started Today
        </UButton>
      </div>
    </div>

    <!-- SVG Divider -->
    <div class="absolute bottom-0 left-0 w-full overflow-hidden line-height-0">
      <svg
        class="relative block w-full h-16 sm:h-20 lg:h-24"
        viewBox="0 0 1200 120"
        preserveAspectRatio="none"
        xmlns="http://www.w3.org/2000/svg"
      >
        <path
          d="M985.66,92.83C906.67,72,823.78,31,743.84,14.19c-82.26-17.34-168.06-16.33-250.45.39-57.84,11.73-114,31.07-172,41.86A600.21,600.21,0,0,1,0,27.35V120H1200V95.8C1132.19,118.92,1055.71,111.31,985.66,92.83Z"
          fill="#FFFFFF"
        />
      </svg>
    </div>
  </section>
</template>

<style scoped>
/* Additional custom styles if needed */
</style>
