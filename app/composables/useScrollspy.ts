import { ref, onUnmounted } from 'vue'

export function useCustomScrollspy() {
  const activeHeadings = ref<string[]>([])
  const visibleHeadings = ref<string[]>([])
  let observer: IntersectionObserver | null = null
  let elements: Element[] = []
  let scrollTimeout: ReturnType<typeof setTimeout> | null = null

  const handleScroll = () => {
    if (scrollTimeout) {
      clearTimeout(scrollTimeout)
    }
    scrollTimeout = setTimeout(() => {
      updateActiveHeading()
    }, 50)
  }

  const updateActiveHeading = () => {
    if (visibleHeadings.value.length === 0) {
      // If no sections are visible, keep the last active heading
      return
    }

    // Find the topmost visible section
    let topMostSection = ''
    let topMostPosition = Infinity

    visibleHeadings.value.forEach((id) => {
      const element = document.getElementById(id)
      if (element) {
        const rect = element.getBoundingClientRect()
        // Consider sections that are at or near the top of the viewport
        if (rect.top <= 100 && rect.top < topMostPosition) {
          topMostPosition = rect.top
          topMostSection = id
        }
      }
    })

    // If no section is at the top, use the first visible section
    if (!topMostSection && visibleHeadings.value.length > 0) {
      // Sort visible headings by their position in the DOM
      const sortedHeadings = visibleHeadings.value.sort((a, b) => {
        const elementA = document.getElementById(a)
        const elementB = document.getElementById(b)
        if (!elementA || !elementB) return 0

        const rectA = elementA.getBoundingClientRect()
        const rectB = elementB.getBoundingClientRect()
        return rectA.top - rectB.top
      })
      topMostSection = sortedHeadings[0] || ''
    }

    // Only update if we have a valid section and it's different from current
    if (topMostSection && !activeHeadings.value.includes(topMostSection)) {
      activeHeadings.value = [topMostSection]
    }
  }

  const updateHeadings = (newElements: Element[]) => {
    // Clean up existing observer and scroll listener
    if (observer) {
      observer.disconnect()
    }
    window.removeEventListener('scroll', handleScroll)

    // Store elements
    elements = newElements.filter(Boolean)
    if (elements.length === 0) {
      activeHeadings.value = []
      visibleHeadings.value = []
      return
    }

    // Create new intersection observer with optimized configuration
    observer = new IntersectionObserver(
      (entries) => {
        entries.forEach((entry) => {
          const id = entry.target.id
          if (!id) return

          if (entry.isIntersecting) {
            // Add to visible headings if not already there
            if (!visibleHeadings.value.includes(id)) {
              visibleHeadings.value = [...visibleHeadings.value, id]
            }
          } else {
            // Remove from visible headings
            visibleHeadings.value = visibleHeadings.value.filter(h => h !== id)
          }
        })

        // Update the active heading based on visible headings
        updateActiveHeading()
      },
      {
        // Use multiple thresholds for better detection
        threshold: [0, 0.1, 0.25],
        // Optimized root margin for better section detection
        rootMargin: '-20% 0px -70% 0px'
      }
    )

    // Start observing all elements
    elements.forEach((element) => {
      if (element) {
        observer?.observe(element)
      }
    })

    // Add scroll event listener for better responsiveness
    window.addEventListener('scroll', handleScroll, { passive: true })

    // Initial check for elements already in view (without setTimeout to avoid race conditions)
    requestAnimationFrame(() => {
      elements.forEach((element) => {
        if (element) {
          const rect = element.getBoundingClientRect()
          const isVisible = rect.top < window.innerHeight * 0.8 && rect.bottom > window.innerHeight * 0.2
          if (isVisible && !visibleHeadings.value.includes(element.id)) {
            visibleHeadings.value = [...visibleHeadings.value, element.id]
          }
        }
      })
      updateActiveHeading()
    })
  }

  // Cleanup on unmount
  onUnmounted(() => {
    if (observer) {
      observer.disconnect()
    }
    window.removeEventListener('scroll', handleScroll)
    if (scrollTimeout) {
      clearTimeout(scrollTimeout)
    }
  })

  return {
    activeHeadings,
    visibleHeadings,
    updateHeadings
  }
}
