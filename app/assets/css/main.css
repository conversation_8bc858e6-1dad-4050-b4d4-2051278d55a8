@import "tailwindcss";
@import "@nuxt/ui-pro";

/* CSS Custom Properties for fonts */
:root {
  --font-sans: 'Inter', sans-serif;
  --font-satoshi: 'Outfit',  sans-serif;
  --font-inter: 'Inter', sans-serif;
}

/* Apply Satoshi font to headings, CTAs, and navigation */
h1, h2, h3, h4, h5, h6 {
  font-family: var(--font-satoshi);
}

/* CTA buttons */
.btn, button, [role="button"] {
  font-family: var(--font-satoshi);
}

/* Navigation elements */
nav, nav a, nav button {
  font-family: var(--font-satoshi);
}

/* Nuxt UI Button component override */
.btn, .u-button {
  font-family: var(--font-satoshi);
}

/* Additional utility classes for fonts */
.font-satoshi {
  font-family: var(--font-satoshi);
}

.font-inter {
  font-family: var(--font-inter);
}

/* Ensure Inter is used for body text and paragraphs */
body, p, span, div, article, section {
  font-family: var(--font-inter);
}

/* Override for specific elements that should use Satoshi */
h1, h2, h3, h4, h5, h6,
.btn, button, [role="button"],
nav, nav a, nav button,
.u-button {
  font-family: var(--font-satoshi);
}

/* Smooth scrolling for anchor links */
html {
  scroll-behavior: smooth;
}

/* SVG divider styles for responsive behavior */
.svg-divider {
  position: absolute;
  bottom: 0;
  left: 0;
  width: 100%;
  overflow: hidden;
  line-height: 0;
}

.svg-divider svg {
  position: relative;
  display: block;
  width: calc(100% + 1.3px);
  height: 60px;
}

@media (min-width: 640px) {
  .svg-divider svg {
    height: 80px;
  }
}

@media (min-width: 1024px) {
  .svg-divider svg {
    height: 96px;
  }
}

/* Ensure sections have proper spacing for dividers */
section {
  position: relative;
}

/* Fix for SVG divider spacing issues */
.line-height-0 {
  line-height: 0;
}

/* Prevent any unwanted spacing around SVG elements */
svg {
  vertical-align: top;
}

/* Mobile Navigation Menu Styling */
/* Target the mobile navigation menu items to make them bigger */
@media (max-width: 1023px) {
  /* Target the specific mobile navigation menu class */
  .mobile-nav-menu {
    padding: 1rem 0 !important;
  }

  /* Target all navigation links and buttons in mobile menu with very high specificity */
  .mobile-nav-menu a,
  .mobile-nav-menu button,
  .mobile-nav-menu [role="menuitem"],
  .mobile-nav-menu [role="button"],
  .mobile-nav-menu li a,
  .mobile-nav-menu li button {
    font-size: 1.3rem !important; /* 20px - larger font size */
    padding: 1.25rem 1.5rem !important; /* Bigger touch targets */
    line-height: 1.4 !important;
    font-weight: 500 !important;
    display: block !important;
    width: 100% !important;
    text-align: left !important;
    border-radius: 0.5rem !important;
    margin: 0.5rem 0 !important;
    transition: all 0.2s ease-in-out !important;
    min-height: 3rem !important; /* Ensure minimum touch target size */
  }

  /* Mobile menu item hover states */
  .mobile-nav-menu a:hover,
  .mobile-nav-menu button:hover,
  .mobile-nav-menu [role="menuitem"]:hover,
  .mobile-nav-menu [role="button"]:hover,
  .mobile-nav-menu li a:hover,
  .mobile-nav-menu li button:hover {
    background-color: #f97316 !important; /* Orange background on hover */
    color: white !important;
    transform: translateX(0.25rem) !important; /* Slight slide effect */
  }

  /* Active menu item styling */
  .mobile-nav-menu a.router-link-active,
  .mobile-nav-menu button.router-link-active,
  .mobile-nav-menu [role="menuitem"].router-link-active,
  .mobile-nav-menu [role="button"].router-link-active,
  .mobile-nav-menu li a.router-link-active,
  .mobile-nav-menu li button.router-link-active {
    background-color: #fed7aa !important; /* Light orange background for active */
    color: #ea580c !important; /* Darker orange text */
    font-weight: 600 !important;
  }

  /* Additional fallback selectors for any nested elements */
  .mobile-nav-menu * {
    font-size: inherit !important;
  }
}
