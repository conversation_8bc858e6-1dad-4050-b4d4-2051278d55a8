# Production license for @nuxt/ui-pro, get one at https://ui.nuxt.com/pro/purchase
NUXT_UI_PRO_LICENSE=

# Firebase Configuration
NUXT_FIREBASE_PROJECT_ID=your-firebase-project-id
NUXT_FIREBASE_CLIENT_EMAIL=<EMAIL>
NUXT_FIREBASE_PRIVATE_KEY="-----BEGIN PRIVATE KEY-----\nYour private key here\n-----END PRIVATE KEY-----\n"

# Email Configuration (Zoho SMTP or other email service)
# Option 1: Standard Zoho SMTP (TLS on port 587)
NUXT_EMAIL_HOST=smtp.zoho.com
NUXT_EMAIL_PORT=587
NUXT_EMAIL_USER=<EMAIL>
NUXT_EMAIL_PASSWORD=your-app-password-from-zoho

# Option 2: Zoho SMTP with SSL (port 465) - try this if 587 doesn't work
# NUXT_EMAIL_HOST=smtp.zoho.com
# NUXT_EMAIL_PORT=465
# NUXT_EMAIL_USER=<EMAIL>
# NUXT_EMAIL_PASSWORD=your-app-password-from-zoho

# Option 3: Alternative Zoho SMTP servers
# NUXT_EMAIL_HOST=smtp.zoho.eu (for EU accounts)
# NUXT_EMAIL_HOST=smtp.zoho.in (for Indian accounts)

# IMPORTANT NOTES:
# 1. Use App Password from Zoho Mail Settings > Security > App Passwords
# 2. NOT your regular Zoho login password
# 3. Enable IMAP/POP access in Zoho Mail settings if required
# 4. Some Zoho accounts may require 2FA to be enabled first
