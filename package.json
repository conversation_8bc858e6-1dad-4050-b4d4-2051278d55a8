{"name": "nuxt-ui-pro-template-landing", "private": true, "type": "module", "scripts": {"build": "nuxt build", "dev": "nuxt dev", "generate": "nuxt generate", "preview": "nuxt preview", "postinstall": "nuxt prepare", "lint": "eslint .", "typecheck": "nuxt typecheck"}, "dependencies": {"@iconify-json/lucide": "^1.2.47", "@iconify-json/simple-icons": "^1.2.38", "@nuxt/image": "^1.10.0", "@nuxt/ui-pro": "^3.1.3", "@nuxtjs/google-fonts": "^3.2.0", "@types/nodemailer": "^6.4.17", "firebase-admin": "^13.4.0", "nodemailer": "^7.0.3", "nuxt": "^3.17.5"}, "devDependencies": {"@nuxt/eslint": "^1.4.1", "eslint": "^9.28.0", "typescript": "^5.8.3", "vue-tsc": "^2.2.10"}, "resolutions": {"unimport": "4.1.1"}}