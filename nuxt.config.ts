// https://nuxt.com/docs/api/configuration/nuxt-config
export default defineNuxtConfig({
  modules: [
    '@nuxt/eslint',
    '@nuxt/image',
    '@nuxt/ui-pro',
    '@nuxtjs/google-fonts'
  ],
  devtools: {
    enabled: true
  },
  css: ['~/assets/css/main.css'],
  colorMode: {
    classSuffix: '',
    preference: 'light', // lock to light
    fallback: 'light',
    storageKey: 'nuxt-color-mode'
  },

  runtimeConfig: {
    // Private keys (only available on the server-side)
    firebaseProjectId: process.env.NUXT_FIREBASE_PROJECT_ID,
    firebaseClientEmail: process.env.NUXT_FIREBASE_CLIENT_EMAIL,
    firebasePrivateKey: process.env.NUXT_FIREBASE_PRIVATE_KEY,
    emailHost: process.env.NUXT_EMAIL_HOST,
    emailPort: process.env.NUXT_EMAIL_PORT,
    emailUser: process.env.NUXT_EMAIL_USER,
    emailPassword: process.env.NUXT_EMAIL_PASSWORD,
    // Public keys (exposed to the client-side)
    public: {
      // Add any public runtime config here if needed
    }
  },

  future: {
    compatibilityVersion: 4
  },

  compatibilityDate: '2025-01-15',
  eslint: {
    config: {
      stylistic: {
        commaDangle: 'never',
        braceStyle: '1tbs'
      }
    }
  },

  googleFonts: {
    families: {
      Inter: [300, 400, 500, 600, 700, 800, 900],
      Outfit: [300, 400, 500, 600, 700]
    },
    display: 'swap',
    preconnect: true,
    prefetch: true,
    preload: true
  },
  uiPro: {
    license: process.env.NUXT_UI_PRO
  }
})
